<script setup lang="ts" name="authorizationhistory">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { View, Download, More, CopyDocument } from '@element-plus/icons-vue'

// 授权历史记录接口
interface AuthorizationRecord {
	id: number
	sequence: number
	permissionName: string
	authDepartment: string
	authRole: string
	conflictCount: number
	signStatus: string
	authTime: string
	permissionStatus: string
	statusType: 'success' | 'warning' | 'danger' | 'info'
}

// 搜索表单
const searchForm = reactive({
	permissionName: '',
	authDepartment: '',
	authTime: ''
})

// 分页数据
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0
})

// 表格数据
const tableData = ref<AuthorizationRecord[]>([])
const loading = ref(false)
const selectedRows = ref<AuthorizationRecord[]>([])
const tableRef = ref()
const buttons = ref([
					{type: 'primary', label: '复制权限', code: 'copy'},
					{type: 'primary', label: '粘贴', code: 'paste',more:true},
					{type: 'primary', label: '共享', code: 'share',more:true},
					{type: 'primary', label: '二次赋权', code: 'reauth',more:true},
					{type: 'primary', label: '签收', code: 'sign',more:true},
				
				])
// 表格列配置
const columns = [
	{ label: '权限名称', prop: 'permissionName' },
	{ label: '授权部门', prop: 'authDepartment' },
	{ label: '授权角色', prop: 'authRole' },
	{ label: '冲突情况', prop: 'conflictCount' },
	{ label: '签收情况', prop: 'signStatus' },
	{ label: '授权时间', prop: 'authTime' },
	{ label: '权限状态', prop: 'permissionStatus' },

]

// 模拟授权历史数据
const mockData: AuthorizationRecord[] = [
	{
		id: 1,
		sequence: 1,
		permissionName: '数据报送任务',
		authDepartment: '张三',
		authRole: '李四',
		conflictCount: 22,
		signStatus: '0/10',
		authTime: '2024-09-03',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 2,
		sequence: 2,
		permissionName: '数据报送任务',
		authDepartment: '李四',
		authRole: '张三',
		conflictCount: 8,
		signStatus: '0/10',
		authTime: '2024-09-03',
		permissionStatus: '已到期',
		statusType: 'danger'
	},
	{
		id: 3,
		sequence: 3,
		permissionName: '数据报送任务',
		authDepartment: '马云',
		authRole: '马化腾',
		conflictCount: 10,
		signStatus: '0/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 4,
		sequence: 4,
		permissionName: '数据报送任务',
		authDepartment: '马化腾',
		authRole: '马云',
		conflictCount: 22,
		signStatus: '1/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 5,
		sequence: 5,
		permissionName: '数据报送任务',
		authDepartment: '马化云',
		authRole: '马雷',
		conflictCount: 22,
		signStatus: '1/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 6,
		sequence: 6,
		permissionName: '数据报送任务',
		authDepartment: '马化雷',
		authRole: '马雨',
		conflictCount: 22,
		signStatus: '0/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 7,
		sequence: 7,
		permissionName: '数据报送任务',
		authDepartment: '马化风',
		authRole: '马雪',
		conflictCount: 22,
		signStatus: '0/10',
		authTime: '2024-04-01',
		permissionStatus: '已到期',
		statusType: 'danger'
	},
	{
		id: 8,
		sequence: 8,
		permissionName: '数据报送任务',
		authDepartment: '马化雷',
		authRole: '马武',
		conflictCount: 22,
		signStatus: '1/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 9,
		sequence: 9,
		permissionName: '数据报送任务',
		authDepartment: '马化雷',
		authRole: '马文',
		conflictCount: 22,
		signStatus: '10/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 10,
		sequence: 10,
		permissionName: '数据报送任务',
		authDepartment: '马化雷',
		authRole: '马文才',
		conflictCount: 22,
		signStatus: '10/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	}
]

// 请求参数
const reqParams = reactive({
	permissionName: '',
	authDepartment: '',
	authTime: '',
	skipCount: 0,
	maxResultCount: 10
})

// 加载数据
const loadData = () => {
	loading.value = true

	setTimeout(() => {
		let filteredData = [...mockData]

		// 根据权限名称筛选
		if (searchForm.permissionName) {
			filteredData = filteredData.filter(item =>
				item.permissionName.includes(searchForm.permissionName)
			)
		}

		// 根据授权部门筛选
		if (searchForm.authDepartment) {
			filteredData = filteredData.filter(item =>
				item.authDepartment.includes(searchForm.authDepartment)
			)
		}

		// 分页处理
		const start = (pagination.page - 1) * pagination.size
		const end = start + pagination.size
		tableData.value = filteredData.slice(start, end)
		pagination.total = filteredData.length

		loading.value = false
	}, 300)
}

// 搜索
const handleSearch = () => {
	loadData()
}

// 重置
const handleReset = () => {
	searchForm.permissionName = ''
	searchForm.authDepartment = ''
	searchForm.authTime = ''
	loadData()
}

// 分页变化处理
const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
	} else {
		pagination.size = val
		pagination.page = 1
	}
	loadData()
}

// 表格按钮点击
const onTableButtonClick = ({ btn, row }: any) => {
	handleActionClick(btn.code, row)
}

// 获取冲突情况的颜色
const getConflictColor = (count: number) => {
	if (count > 20) return '#ff4d4f'
	if (count > 10) return '#faad14'
	if (count > 0) return '#52c41a'
	return '#1890ff'
}

// 按钮点击处理（暂时只显示消息）
const handleButtonClick = (action: string) => {
	ElMessage.info(`${action} 功能开发中...`)
}

// 操作菜单点击
const handleActionClick = (action: string, row?: AuthorizationRecord) => {
	if (row) {
		ElMessage.info(`对 "${row.permissionName}" 执行 ${action} 操作`)
	} else {
		ElMessage.info(`${action} 功能开发中...`)
	}
}

// 删除不需要的方法，因为现在使用 defaultTableData

// 组件挂载时加载数据
onMounted(() => {
	loadData()
})
</script>

<template>
	<div class="authorization-history">
		<Block
			title="授权历史"
			:enable-fixed-height="true"
			:enable-expand-content="true"
		>
			<template #expand>
				<!-- 搜索区域 -->
				<div class="search-area">
					<el-form :model="searchForm" :inline="true" label-width="auto">
						<el-form-item>
							<el-input
								v-model="searchForm.permissionName"
								placeholder="请输入权限名称"
								style="width: 200px;"
								clearable
							/>
						</el-form-item>
						<el-form-item>
							<el-input
								v-model="searchForm.authDepartment"
								placeholder="请输入授权部门"
								style="width: 200px;"
								clearable
							/>
						</el-form-item>
						<el-form-item>
							<el-select
								v-model="searchForm.authTime"
								placeholder="请选择授权时间"
								style="width: 200px;"
								clearable
							>
								<el-option label="今天" value="today" />
								<el-option label="本周" value="week" />
								<el-option label="本月" value="month" />
							</el-select>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" @click="handleSearch">查询</el-button>
							<el-button @click="handleReset">重置</el-button>
						</el-form-item>
					</el-form>
				</div>
			</template>
			
			<!-- 表格区域 -->
			<TableV2
				ref="tableRef"
				:defaultTableData="tableData"
				:columns="columns"
				:enable-toolbar="true"
				:auto-height="true"
				:enable-own-button="false"
				:buttons="buttons"
				@click-button="onTableButtonClick"
			>
			<template #toolbarLeft>
					<!-- 功能按钮 -->
					<el-button type="primary" @click="handleButtonClick('全部复制')">
								全部复制
							</el-button>

							<el-dropdown @command="handleActionClick">
								<el-button type="primary">
									批量粘贴<el-icon class="el-icon--right"><arrow-down /></el-icon>
								</el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item command="粘贴选择">粘贴选择</el-dropdown-item>
										<el-dropdown-item command="粘贴全部">粘贴全部</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>

							<el-dropdown @command="handleActionClick">
								<el-button type="primary">
									批量共享<el-icon class="el-icon--right"><arrow-down /></el-icon>
								</el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item command="共享选择">共享选择</el-dropdown-item>
										<el-dropdown-item command="共享全部">共享全部</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>

							<el-button type="primary" @click="handleButtonClick('二次赋权规则')">
								二次赋权规则
							</el-button>

							<el-button type="primary" @click="handleButtonClick('签收记录管理')">
								签收记录管理
							</el-button>

							<el-button type="primary" @click="handleButtonClick('授权冲突检测')">
								授权冲突检测
							</el-button>

							<el-button type="primary" @click="handleButtonClick('部门视图可视化分析')">
								部门视图可视化分析
							</el-button>
							
							<el-button type="primary" @click="handleButtonClick('签收流程')">
								签收流程
							</el-button>
			</template>
				<template #conflictCount="{ row }">
					<span :style="{ color: getConflictColor(row.conflictCount) }">
						{{ row.conflictCount }}个
					</span>
				</template>
				<template #permissionStatus="{ row }">
					<el-tag :type="row.statusType">
						{{ row.permissionStatus }}
					</el-tag>
				</template>
			</TableV2>

			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			/>
		</Block>
	</div>
</template>

<route>
{
	meta: {
		title: '授权历史',
		ignoreLabel: false
	}
}
</route>

<style scoped lang="scss">
.authorization-history {
	.search-area {
		margin-bottom: 20px;
		padding: 15px;
		background: #f5f7fa;
		border-radius: 4px;
	}



	.table-area {
		margin-bottom: 20px;
	}

	.pagination-area {
		display: flex;
		justify-content: center;
	}
}
</style>
