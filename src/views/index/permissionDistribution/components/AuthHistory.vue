<script setup lang="ts" name="authhistory">
import {reactive, ref, computed} from 'vue'
import {ElMessage} from 'element-plus'
import {ArrowDown} from '@element-plus/icons-vue'

// 授权历史筛选条件
const authHistoryFilter = reactive({
	authStatus: ''
})

// 授权历史选中的记录
const selectedAuthRecords = ref([])

// 授权历史模拟数据
const authHistoryData = ref([
	{
		id: 1,
		permissionName: '数据报送任务',
		authDepartment: '张三',
		authRole: '李四',
		conflictSituation: '22个',
		signSituation: '0/10',
		authTime: '2024-09-03',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 2,
		permissionName: '数据报送任务',
		authDepartment: '李四',
		authRole: '张三',
		conflictSituation: '6个',
		signSituation: '0/10',
		authTime: '2024-09-03',
		permissionStatus: '已到期',
		statusType: 'danger'
	},
	{
		id: 3,
		permissionName: '数据报送任务',
		authDepartment: '马云',
		authRole: '马化腾',
		conflictSituation: '10个',
		signSituation: '0/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 4,
		permissionName: '数据报送任务',
		authDepartment: '马化腾',
		authRole: '马云',
		conflictSituation: '22个',
		signSituation: '1/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 5,
		permissionName: '数据报送任务',
		authDepartment: '马化云',
		authRole: '马雷',
		conflictSituation: '22个',
		signSituation: '1/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 6,
		permissionName: '数据报送任务',
		authDepartment: '马化雷',
		authRole: '马雨',
		conflictSituation: '22个',
		signSituation: '0/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 7,
		permissionName: '数据报送任务',
		authDepartment: '马化风',
		authRole: '马雪',
		conflictSituation: '22个',
		signSituation: '0/10',
		authTime: '2024-04-01',
		permissionStatus: '已到期',
		statusType: 'danger'
	},
	{
		id: 8,
		permissionName: '数据报送任务',
		authDepartment: '马化雷',
		authRole: '马武',
		conflictSituation: '22个',
		signSituation: '1/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 9,
		permissionName: '数据报送任务',
		authDepartment: '马化雷',
		authRole: '马文',
		conflictSituation: '22个',
		signSituation: '10/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	},
	{
		id: 10,
		permissionName: '数据报送任务',
		authDepartment: '马化雷',
		authRole: '马文才',
		conflictSituation: '22个',
		signSituation: '10/10',
		authTime: '2024-04-01',
		permissionStatus: '授权中',
		statusType: 'warning'
	}
])

// 授权状态选项
const authStatusOptions = [
	{ label: '全部', value: '' },
	{ label: '授权中', value: '授权中' },
	{ label: '已到期', value: '已到期' },
	{ label: '已撤销', value: '已撤销' }
]

// 授权历史相关方法
const handleAuthSelectionChange = (selection: any) => {
	selectedAuthRecords.value = selection
}

// 授权历史操作方法
const handleCopyAll = () => {
	ElMessage.success('全部复制操作成功')
}

const handleBatchPaste = () => {
	if (selectedAuthRecords.value.length === 0) {
		ElMessage.warning('请选择要粘贴的记录')
		return
	}
	ElMessage.success(`批量粘贴 ${selectedAuthRecords.value.length} 条记录`)
}

const handlePasteAll = () => {
	ElMessage.success('粘贴全部操作成功')
}

const handleBatchShare = () => {
	if (selectedAuthRecords.value.length === 0) {
		ElMessage.warning('请选择要共享的记录')
		return
	}
	ElMessage.success(`批量共享 ${selectedAuthRecords.value.length} 条记录`)
}

const handleShareAll = () => {
	ElMessage.success('共享全部操作成功')
}

const handleSecondaryAuthRule = () => {
	ElMessage.info('二次赋权规则管理')
}

const handleSignRecordManage = () => {
	ElMessage.info('签收记录管理')
}

const handleAuthConflictDetect = () => {
	ElMessage.info('授权冲突检测')
}

const handleDeptViewAnalysis = () => {
	ElMessage.info('部门视图可视化分析')
}

const handleSignProcess = () => {
	ElMessage.info('签收流程')
}

// 单条记录操作
const handleCopyPermission = (record: any) => {
	ElMessage.success(`复制权限：${record.permissionName}`)
}

const handlePastePermission = (record: any) => {
	ElMessage.success(`粘贴权限：${record.permissionName}`)
}

const handleSharePermission = (record: any) => {
	ElMessage.success(`共享权限：${record.permissionName}`)
}

const handleSecondaryAuth = (record: any) => {
	ElMessage.info(`二次赋权：${record.permissionName}`)
}

const handleSignPermission = (record: any) => {
	ElMessage.info(`签收权限：${record.permissionName}`)
}

// 更多操作处理
const handleMoreAction = (command: string, record: any) => {
	switch (command) {
		case 'paste':
			handlePastePermission(record)
			break
		case 'share':
			handleSharePermission(record)
			break
		case 'secondaryAuth':
			handleSecondaryAuth(record)
			break
		case 'sign':
			handleSignPermission(record)
			break
	}
}

// 重置授权历史筛选
const resetAuthFilter = () => {
	authHistoryFilter.authStatus = ''
}

// 筛选后的授权历史数据
const filteredAuthHistory = computed(() => {
	if (!authHistoryFilter.authStatus) {
		return authHistoryData.value
	}
	return authHistoryData.value.filter(record => 
		record.permissionStatus === authHistoryFilter.authStatus
	)
})
</script>

<template>
	<div class="auth-history-content">
		<!-- 筛选条件 -->
		<div class="auth-filter">
			<el-form :model="authHistoryFilter" inline>
				<el-form-item label="授权状态">
					<el-select 
						v-model="authHistoryFilter.authStatus" 
						placeholder="请选择授权状态"
						clearable
						style="width: 200px"
					>
						<el-option
							v-for="item in authStatusOptions"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button @click="resetAuthFilter">重置</el-button>
				</el-form-item>
			</el-form>
		</div>
		
		<!-- 操作按钮 -->
		<div class="auth-actions">
			<el-button size="small" type="primary" @click="handleCopyAll">全部复制</el-button>
			<el-button size="small" type="primary" @click="handleBatchPaste">批量粘贴</el-button>
			<el-button size="small" type="primary" @click="handlePasteAll">粘贴全部</el-button>
			<el-button size="small" type="primary" @click="handleBatchShare">批量共享</el-button>
			<el-button size="small" type="primary" @click="handleShareAll">共享全部</el-button>
			<el-button size="small" type="primary" @click="handleSecondaryAuthRule">二次赋权规则</el-button>
			<el-button size="small" type="primary" @click="handleSignRecordManage">签收记录管理</el-button>
			<el-button size="small" type="primary" @click="handleAuthConflictDetect">授权冲突检测</el-button>
			<el-button size="small" type="primary" @click="handleDeptViewAnalysis">部门视图可视化分析</el-button>
			<el-button size="small" type="primary" @click="handleSignProcess">签收流程</el-button>
		</div>
		
		<!-- 授权历史表格 -->
		<el-table 
			:data="filteredAuthHistory" 
			style="width: 100%; margin-top: 16px"
			@selection-change="handleAuthSelectionChange"
		>
			<el-table-column type="selection" width="55" />
			<el-table-column label="序号" width="80" type="index" :index="1" />
			<el-table-column label="权限名称" prop="permissionName" width="150" />
			<el-table-column label="授权部门" prop="authDepartment" width="120" />
			<el-table-column label="授权角色" prop="authRole" width="120" />
			<el-table-column label="冲突情况" prop="conflictSituation" width="100">
				<template #default="scope">
					<span style="color: #f56c6c">{{ scope.row.conflictSituation }}</span>
				</template>
			</el-table-column>
			<el-table-column label="签收情况" prop="signSituation" width="100" />
			<el-table-column label="授权时间" prop="authTime" width="120" />
			<el-table-column label="权限状态" prop="permissionStatus" width="100">
				<template #default="scope">
					<el-tag :type="scope.row.statusType">
						{{ scope.row.permissionStatus }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column label="操作" width="200">
				<template #default="scope">
					<el-button 
						type="text" 
						size="small" 
						@click="handleCopyPermission(scope.row)"
					>
						复制权限
					</el-button>
					<el-dropdown @command="(command) => handleMoreAction(command, scope.row)">
						<el-button type="text" size="small">
							更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
						</el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item command="paste">粘贴</el-dropdown-item>
								<el-dropdown-item command="share">共享</el-dropdown-item>
								<el-dropdown-item command="secondaryAuth">二次赋权</el-dropdown-item>
								<el-dropdown-item command="sign">签收</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<style scoped lang="scss">
.auth-history-content {
	.auth-filter {
		margin-bottom: 16px;
		padding: 16px;
		background-color: #f5f7fa;
		border-radius: 4px;
	}
	
	.auth-actions {
		display: flex;
		flex-wrap: wrap;
		gap: 8px;
		margin-bottom: 16px;
		
		.el-button {
			margin: 0;
		}
	}
}
</style>
