<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 权限项接口
interface PermissionItem {
	id: number
	name: string
	description: string
	type: string
	status: string
}

// Props
interface Props {
	modelValue: boolean
	title?: string
	width?: string
}

const props = withDefaults(defineProps<Props>(), {
	title: '权限合并',
	width: '600px'
})

// Emits
const emit = defineEmits<{
	'update:modelValue': [value: boolean]
}>()

// 响应式数据
const dialogVisible = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value)
})

// 表单数据
const formData = reactive({
	mergePermission: '', // 合并权限（单选）
	targetPermissions: [] as string[] // 被合并权限（多选）
})

// 模拟权限数据
const permissionOptions = ref<PermissionItem[]>([
	{ id: 1, name: '用户管理权限', description: '管理系统用户信息', type: '管理权限', status: '启用' },
	{ id: 2, name: '数据查看权限', description: '查看业务数据', type: '查看权限', status: '启用' },
	{ id: 3, name: '报表生成权限', description: '生成各类报表', type: '操作权限', status: '启用' },
	{ id: 4, name: '系统配置权限', description: '配置系统参数', type: '管理权限', status: '启用' },
	{ id: 5, name: '审批流程权限', description: '处理审批流程', type: '操作权限', status: '启用' },
	{ id: 6, name: '数据导出权限', description: '导出业务数据', type: '操作权限', status: '启用' },
	{ id: 7, name: '部门管理权限', description: '管理部门信息', type: '管理权限', status: '启用' },
	{ id: 8, name: '角色分配权限', description: '分配用户角色', type: '管理权限', status: '启用' },
	{ id: 9, name: '日志查看权限', description: '查看系统日志', type: '查看权限', status: '启用' },
	{ id: 10, name: '备份恢复权限', description: '数据备份与恢复', type: '管理权限', status: '启用' }
])

// 可选择的被合并权限（排除已选择的合并权限）
const availableTargetPermissions = computed(() => {
	return permissionOptions.value.filter(item => item.name !== formData.mergePermission)
})

// 重置表单
const resetForm = () => {
	formData.mergePermission = ''
	formData.targetPermissions = []
}

// 验证表单
const validateForm = () => {
	if (!formData.mergePermission) {
		ElMessage.warning('请选择合并权限')
		return false
	}
	if (formData.targetPermissions.length === 0) {
		ElMessage.warning('请选择至少一个被合并权限')
		return false
	}
	return true
}

// 保存合并记录到本地存储
const saveMergeRecord = (mergePermission: string, targetPermissions: string[], isFullMerge: boolean = false) => {
	const record = {
		id: Date.now(),
		sequence: 0, // 将在记录组件中重新计算
		mergePermission,
		mergedPermissions: [...targetPermissions],
		mergeTime: new Date().toLocaleString('zh-CN'),
		operator: '当前用户',
		status: '已完成'
	}

	// 从本地存储获取现有记录
	const existingRecords = JSON.parse(localStorage.getItem('permissionMergeRecords') || '[]')
	existingRecords.unshift(record) // 添加到开头

	// 保存到本地存储
	localStorage.setItem('permissionMergeRecords', JSON.stringify(existingRecords))
}

// 权限合并
const handlePermissionMerge = () => {
	if (!validateForm()) return

	ElMessageBox.confirm(
		`确认将 ${formData.targetPermissions.length} 个权限合并到 "${formData.mergePermission}" 中吗？`,
		'权限合并确认',
		{
			confirmButtonText: '确认合并',
			cancelButtonText: '取消',
			type: 'warning'
		}
	).then(() => {
		// 保存合并记录
		saveMergeRecord(formData.mergePermission, formData.targetPermissions)

		// 模拟合并操作
		ElMessage.success(`成功将 ${formData.targetPermissions.length} 个权限合并到 "${formData.mergePermission}"`)
		resetForm()
		dialogVisible.value = false
	}).catch(() => {
		// 用户取消操作
	})
}

// 全部合并
const handleFullMerge = () => {
	if (!formData.mergePermission) {
		ElMessage.warning('请先选择合并权限')
		return
	}

	const allOtherPermissions = availableTargetPermissions.value.map(item => item.name)

	ElMessageBox.confirm(
		`确认将所有其他权限（${allOtherPermissions.length} 个）合并到 "${formData.mergePermission}" 中吗？`,
		'全部合并确认',
		{
			confirmButtonText: '确认合并',
			cancelButtonText: '取消',
			type: 'warning'
		}
	).then(() => {
		// 保存合并记录
		saveMergeRecord(formData.mergePermission, allOtherPermissions, true)

		// 模拟全部合并操作
		ElMessage.success(`成功将所有权限合并到 "${formData.mergePermission}"`)
		resetForm()
		dialogVisible.value = false
	}).catch(() => {
		// 用户取消操作
	})
}

// 监听合并权限变化，清空被合并权限选择
watch(() => formData.mergePermission, () => {
	formData.targetPermissions = []
})

// 关闭弹窗时重置表单
watch(dialogVisible, (newVal) => {
	if (!newVal) {
		resetForm()
	}
})
</script>

<template>
	<Dialog
		v-model="dialogVisible"
		:title="title"
		:width="width"
		:destroy-on-close="true"
	>
		<div class="permission-combined-form">
			<el-form :model="formData" label-width="120px" label-position="left">
				<el-form-item label="合并权限：" required>
					<el-select
						v-model="formData.mergePermission"
						placeholder="请选择合并权限"
						style="width: 100%;"
						clearable
					>
						<el-option
							v-for="option in permissionOptions"
							:key="option.id"
							:label="option.name"
							:value="option.name"
						>
							<div style="display: flex; justify-content: space-between;">
								<span>{{ option.name }}</span>
								<span style="color: #8492a6; font-size: 12px;">{{ option.type }}</span>
							</div>
						</el-option>
					</el-select>
					<div class="form-tip">选择作为合并目标的权限</div>
				</el-form-item>

				<el-form-item label="被合并权限：" required>
					<el-select
						v-model="formData.targetPermissions"
						placeholder="请选择被合并权限"
						style="width: 100%;"
						multiple
						collapse-tags
						collapse-tags-tooltip
						:max-collapse-tags="3"
						clearable
					>
						<el-option
							v-for="option in availableTargetPermissions"
							:key="option.id"
							:label="option.name"
							:value="option.name"
						>
							<div style="display: flex; justify-content: space-between;">
								<span>{{ option.name }}</span>
								<span style="color: #8492a6; font-size: 12px;">{{ option.type }}</span>
							</div>
						</el-option>
					</el-select>
					<div class="form-tip">选择需要被合并的权限（可多选）</div>
				</el-form-item>
			</el-form>

			<!-- 操作按钮 -->
			<div class="button-group">
				<el-button type="primary" @click="handlePermissionMerge">
					权限合并
				</el-button>
				<el-button type="warning" @click="handleFullMerge">
					全部合并
				</el-button>
			</div>

			<!-- 合并预览 -->
			<div v-if="formData.mergePermission && formData.targetPermissions.length > 0" class="merge-preview">
				<el-divider content-position="left">合并预览</el-divider>
				<div class="preview-content">
					<div class="merge-target">
						<el-tag type="success" size="large">{{ formData.mergePermission }}</el-tag>
						<span class="merge-label">（合并目标）</span>
					</div>
					<div class="merge-arrow">⬅</div>
					<div class="merge-sources">
						<el-tag
							v-for="permission in formData.targetPermissions"
							:key="permission"
							type="info"
							size="small"
							style="margin: 2px;"
						>
							{{ permission }}
						</el-tag>
					</div>
				</div>
			</div>
		</div>
	</Dialog>
</template>

<style scoped lang="scss">
.permission-combined-form {
	padding: 20px 0;

	.form-tip {
		font-size: 12px;
		color: #909399;
		margin-top: 5px;
	}

	.button-group {
		display: flex;
		justify-content: center;
		gap: 15px;
		margin: 30px 0 20px 0;
	}

	.merge-preview {
		margin-top: 20px;
		padding: 15px;
		background: #f5f7fa;
		border-radius: 4px;

		.preview-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-wrap: wrap;
			gap: 10px;

			.merge-target {
				display: flex;
				align-items: center;
				gap: 8px;

				.merge-label {
					font-size: 12px;
					color: #67c23a;
				}
			}

			.merge-arrow {
				font-size: 18px;
				color: #409eff;
				font-weight: bold;
			}

			.merge-sources {
				display: flex;
				flex-wrap: wrap;
				max-width: 300px;
			}
		}
	}
}
</style>
