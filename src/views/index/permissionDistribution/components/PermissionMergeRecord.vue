<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { View, Download } from '@element-plus/icons-vue'

// 权限合并记录接口
interface PermissionMergeRecord {
	id: number
	sequence: number
	mergePermission: string
	mergedPermissions: string[]
	mergeTime: string
	operator: string
	status: string
}

// Props
interface Props {
	modelValue: boolean
	title?: string
	width?: string
}

const props = withDefaults(defineProps<Props>(), {
	title: '权限合并记录',
	width: '1200px'
})

// Emits
const emit = defineEmits<{
	'update:modelValue': [value: boolean]
}>()

// 响应式数据
const dialogVisible = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value)
})

// 搜索表单
const searchForm = reactive({
	mergePermission: ''
})

// 分页数据
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0
})

// 表格数据
const tableData = ref<PermissionMergeRecord[]>([])
const loading = ref(false)
const selectedRows = ref<PermissionMergeRecord[]>([])

// 模拟权限合并记录数据
const mockData: PermissionMergeRecord[] = [
	{
		id: 1,
		sequence: 1,
		mergePermission: '用户管理权限',
		mergedPermissions: ['数据查看权限', '报表生成权限'],
		mergeTime: '2024-01-15 10:30:00',
		operator: '张三',
		status: '已完成'
	},
	{
		id: 2,
		sequence: 2,
		mergePermission: '系统配置权限',
		mergedPermissions: ['审批流程权限', '数据导出权限', '部门管理权限'],
		mergeTime: '2024-01-14 14:20:00',
		operator: '李四',
		status: '已完成'
	},
	{
		id: 3,
		sequence: 3,
		mergePermission: '角色分配权限',
		mergedPermissions: ['日志查看权限'],
		mergeTime: '2024-01-13 09:15:00',
		operator: '王五',
		status: '已完成'
	},
	{
		id: 4,
		sequence: 4,
		mergePermission: '备份恢复权限',
		mergedPermissions: ['数据查看权限', '系统配置权限', '审批流程权限'],
		mergeTime: '2024-01-12 16:45:00',
		operator: '赵六',
		status: '已完成'
	},
	{
		id: 5,
		sequence: 5,
		mergePermission: '用户管理权限',
		mergedPermissions: ['角色分配权限', '部门管理权限'],
		mergeTime: '2024-01-11 11:30:00',
		operator: '钱七',
		status: '已完成'
	}
]

// 表格列配置
const columns = [
	{ label: '序号', prop: 'sequence', width: '80px' },
	{ label: '合并权限', prop: 'mergePermission', width: '200px' },
	{ label: '被合并权限', prop: 'mergedPermissions', width: '300px' },
	{ label: '合并时间', prop: 'mergeTime', width: '180px' },
	{ label: '操作', prop: 'operation', width: '150px' }
]

// 权限选项（用于筛选）
const permissionOptions = ref([
	'用户管理权限',
	'数据查看权限',
	'报表生成权限',
	'系统配置权限',
	'审批流程权限',
	'数据导出权限',
	'部门管理权限',
	'角色分配权限',
	'日志查看权限',
	'备份恢复权限'
])

// 获取所有数据（本地存储 + 模拟数据）
const getAllData = () => {
	// 从本地存储获取实际合并记录
	const localRecords = JSON.parse(localStorage.getItem('permissionMergeRecords') || '[]')

	// 合并本地记录和模拟数据
	const allData = [...localRecords, ...mockData]

	// 重新计算序号
	allData.forEach((item, index) => {
		item.sequence = index + 1
	})

	return allData
}

// 加载数据
const loadData = () => {
	loading.value = true

	// 模拟API调用
	setTimeout(() => {
		let filteredData = getAllData()

		// 根据合并权限筛选
		if (searchForm.mergePermission) {
			filteredData = filteredData.filter(item =>
				item.mergePermission.includes(searchForm.mergePermission)
			)
		}

		// 分页处理
		const start = (pagination.page - 1) * pagination.size
		const end = start + pagination.size
		tableData.value = filteredData.slice(start, end)
		pagination.total = filteredData.length

		loading.value = false
	}, 300)
}

// 搜索
const handleSearch = () => {
	pagination.page = 1
	loadData()
}

// 重置
const handleReset = () => {
	searchForm.mergePermission = ''
	pagination.page = 1
	loadData()
}

// 分页变化
const handlePageChange = (page: number) => {
	pagination.page = page
	loadData()
}

const handleSizeChange = (size: number) => {
	pagination.size = size
	pagination.page = 1
	loadData()
}

// 查看详情
const handleView = (row: PermissionMergeRecord) => {
	const mergedPermissionsText = row.mergedPermissions.join('、')
	ElMessageBox.alert(
		`<div style="line-height: 1.6;">
			<p><strong>合并权限：</strong>${row.mergePermission}</p>
			<p><strong>被合并权限：</strong>${mergedPermissionsText}</p>
			<p><strong>合并时间：</strong>${row.mergeTime}</p>
			<p><strong>操作人员：</strong>${row.operator}</p>
			<p><strong>状态：</strong>${row.status}</p>
		</div>`,
		'权限合并详情',
		{
			dangerouslyUseHTMLString: true,
			confirmButtonText: '确定'
		}
	)
}

// 导出单条记录
const handleExport = (row: PermissionMergeRecord) => {
	const exportData = {
		序号: row.sequence,
		合并权限: row.mergePermission,
		被合并权限: row.mergedPermissions.join('、'),
		合并时间: row.mergeTime,
		操作人员: row.operator,
		状态: row.status
	}
	
	// 模拟导出
	const dataStr = JSON.stringify(exportData, null, 2)
	const blob = new Blob([dataStr], { type: 'application/json' })
	const url = URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = `权限合并记录_${row.id}.json`
	link.click()
	URL.revokeObjectURL(url)
	
	ElMessage.success('导出成功')
}

// 表格选择变化
const handleSelectionChange = (selection: PermissionMergeRecord[]) => {
	selectedRows.value = selection
}

// 批量导出
const handleBatchExport = () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请先勾选要导出的记录')
		return
	}

	const exportData = selectedRows.value.map(row => ({
		序号: row.sequence,
		合并权限: row.mergePermission,
		被合并权限: row.mergedPermissions.join('、'),
		合并时间: row.mergeTime,
		操作人员: row.operator,
		状态: row.status
	}))

	const dataStr = JSON.stringify(exportData, null, 2)
	const blob = new Blob([dataStr], { type: 'application/json' })
	const url = URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = `权限合并记录_批量导出_${new Date().getTime()}.json`
	link.click()
	URL.revokeObjectURL(url)

	ElMessage.success(`成功导出 ${selectedRows.value.length} 条记录`)
}

// 导出全部
const handleExportAll = () => {
	const allData = getAllData()
	const exportData = allData.map(row => ({
		序号: row.sequence,
		合并权限: row.mergePermission,
		被合并权限: row.mergedPermissions.join('、'),
		合并时间: row.mergeTime,
		操作人员: row.operator,
		状态: row.status
	}))

	const dataStr = JSON.stringify(exportData, null, 2)
	const blob = new Blob([dataStr], { type: 'application/json' })
	const url = URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = `权限合并记录_全部导出_${new Date().getTime()}.json`
	link.click()
	URL.revokeObjectURL(url)

	ElMessage.success(`成功导出全部 ${allData.length} 条记录`)
}

// 组件挂载时加载数据
onMounted(() => {
	if (dialogVisible.value) {
		loadData()
	}
})

// 监听弹窗打开
const handleDialogOpen = () => {
	loadData()
}
</script>

<template>
	<Dialog
		v-model="dialogVisible"
		:title="title"
		:width="width"
		:destroy-on-close="true"
		@open="handleDialogOpen"
	>
		<div class="permission-merge-record">
			<!-- 搜索区域 -->
			<div class="search-area">
				<el-form :model="searchForm" :inline="true" label-width="100px">
					<el-form-item label="合并权限：">
						<el-select
							v-model="searchForm.mergePermission"
							placeholder="请选择合并权限"
							style="width: 200px;"
							clearable
						>
							<el-option
								v-for="option in permissionOptions"
								:key="option"
								:label="option"
								:value="option"
							/>
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button type="primary" @click="handleSearch">查询</el-button>
						<el-button @click="handleReset">重置</el-button>
					</el-form-item>
				</el-form>
			</div>

			<!-- 操作按钮区域 -->
			<div class="action-area">
				<el-button type="primary" @click="handleBatchExport" :icon="Download">
					批量导出
				</el-button>
				<el-button type="success" @click="handleExportAll" :icon="Download">
					导出全部
				</el-button>
			</div>

			<!-- 表格区域 -->
			<div class="table-area">
				<el-table
					:data="tableData"
					:loading="loading"
					border
					stripe
					style="width: 100%"
					@selection-change="handleSelectionChange"
				>
					<el-table-column
						type="selection"
						width="55"
						align="center"
					/>
					<el-table-column
						prop="sequence"
						label="序号"
						align="center"
					/>
					<el-table-column
						prop="mergePermission"
						label="合并权限"
						show-overflow-tooltip
					/>
					<el-table-column
						prop="mergedPermissions"
						label="被合并权限"
						show-overflow-tooltip
					>
						<template #default="{ row }">
							<el-tag
								v-for="permission in row.mergedPermissions"
								:key="permission"
								size="small"
								style="margin: 2px;"
							>
								{{ permission }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column
						prop="mergeTime"
						label="合并时间"
						align="center"
					/>
					<el-table-column
						label="操作"
						align="center"
					>
						<template #default="{ row }">
							<el-button
								size="small"
								type="primary"
								:icon="View"
								@click="handleView(row)"
							>
								查看
							</el-button>
							<el-button
								size="small"
								type="success"
								:icon="Download"
								@click="handleExport(row)"
							>
								导出
							</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>

			<!-- 分页区域 -->
			<div class="pagination-area">
				<el-pagination
					v-model:current-page="pagination.page"
					v-model:page-size="pagination.size"
					:page-sizes="[10, 20, 50, 100]"
					:total="pagination.total"
					layout="total, sizes, prev, pager, next, jumper"
					@size-change="handleSizeChange"
					@current-change="handlePageChange"
				/>
			</div>
		</div>
	</Dialog>
</template>

<style scoped lang="scss">
.permission-merge-record {
	.search-area {
		margin-bottom: 20px;
		padding: 15px;
		background: #f5f7fa;
		border-radius: 4px;
	}

	.action-area {
		margin-bottom: 15px;
		text-align: right;

		.el-button {
			margin-left: 10px;
		}
	}

	.table-area {
		margin-bottom: 20px;
	}

	.pagination-area {
		display: flex;
		justify-content: center;
	}
}
</style>
