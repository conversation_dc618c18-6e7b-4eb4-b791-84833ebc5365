<script setup lang="ts" name="permissiondistribution">
import {reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage, ElMessageBox} from 'element-plus'
import departmentFavoriteComp from '@/components/common/department-favorite-comp.vue'
import {surplusDate, ReportsFlowStatusType, ReportsFlowStatus} from '@/define/statement.define'
import {GetPlanTaskProgress} from '@/api/ReportApi'
import DataPermissions from './components/DataPermissions.vue'
import YJModel from './components/YJModel.vue'
import YJProblem from './components/YJProblem.vue'
import QXCategory from './components/QXCategory.vue'
import QXAllocationBackup from './components/QXAllocationBackup.vue'
import QXOperationRecord from './components/QXOperationRecord.vue'
import TemporaryAuthorization from './components/TemporaryAuthorization.vue'

import OperatePermissions from './components/OperatePermissions.vue'

import AnalysisReportLabel from './components/AnalysisReportLabel.vue'
import AllocationRecord from './components/AllocationRecord.vue'
import AllocationReport from './components/AllocationReport.vue'
import MenuPermissions from './components/MenuPermissions.vue'
import AllocationExplanation from './components/AllocationExplanation.vue'
import PermissionDistributionChart from './components/PermissionDistributionChart.vue'
import OperationVideo from './components/OperationVideo.vue'
import LifeCycle from './components/LifeCycle.vue'
import PermissionVersion from './components/PermissionVersion.vue'
import OperatingManual from './components/OperatingManual.vue'
import ReminderContent from './components/ReminderContent.vue'
import ChangeLog from './components/ChangeLog.vue'
import ReceiveSettings from './components/ReceiveSettings.vue'
import VolumeLicense from './components/VolumeLicense.vue'
import PermissionRecord from './components/PermissionRecord.vue'
import PermissionCancel from './components/PermissionCancel.vue'
import PermissionUsageReport from './components/PermissionUsageReport.vue'
import PermissionTemplate from './components/PermissionTemplate.vue'
import PermissionComparison from './components/PermissionComparison.vue'
import PermissionComparisonReport from './components/PermissionComparisonReport.vue'
import PermissionOptimizationRule from './components/PermissionOptimizationRule.vue'
import PermissionChangeNotification from './components/PermissionChangeNotification.vue'
import PermissionApplicationReport from './components/PermissionApplicationReport.vue'
import PermissionCombined from './components/PermissionCombined.vue'
import PermissionMergeRecord from './components/PermissionMergeRecord.vue'
const router = useRouter()
const loading = ref(false)
const show = ref(false)
const showDataPermissions = ref(false)
const showOperatePermissions = ref(false)

const checkDepartmentList = ref([])
const defaultCheckedData = ref([])
const defaultCheckUserData = ref([])

const formProps = ref([{label: '任务名称', prop: 'name', type: 'text'}])
const form = ref({name: ''})
const nodeData = ref<any[]>([])
const optionSelect = [
	{label: '列表转换', value: '列表转换'},
	{label: '时间格式转换', value: '时间格式转换'},
	{label: '标准转换', value: '标准转换'},
	{label: '字典替换', value: '字典替换'},
	{label: '多字段标准转换', value: '多字段标准转换'},
]
const columns = [
	{label: '任务名称', prop: 'name'},
	{label: '填报范围', prop: 'fillingRange'},
	{label: '填报情况', prop: 'departmentReportingStatus'},
	{label: '截止时间', prop: 'endDate'},
	{label: '创建日期', prop: 'creationTime'},
	{label: '任务状态', prop: 'status'},
]

const tableRef = ref()
const tableHeight = ref(0)
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})
const reqParams = reactive({
	name: '',
	skipCount: 0,
	maxResultCount: 10,
})
const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

const onBeforeComplete = ({items, next}: any) => {
	const newData: any = []
	items.forEach((item: any) => {
		newData.push({
			...item,
			remindDays: surplusDate(item.fillingPeriodType, item.newToDays, item.endDate),
		})
	})
	next(newData)
}

const getPlanTaskProgress = () => {
	loading.value = true
	const tableData = tableRef.value?.getTableData()
	GetPlanTaskProgress(tableData.map((item: any) => item.id))
		.then((res: any) => {
			res.data.forEach((item: any) => {
				const task = tableData.find((x: any) => x.id === item.planTaskId)
				if (task) {
					task.departmentReportingStatus = item.reportTaskStatus
				}
			})
		})
		.catch((err: any) => {
			window.errMsg(err, '获取进度')
		})
		.finally(() => {
			loading.value = false
		})
}

const onTableButtonClick = ({btn, row}: any) => {
	show.value = true
}

const onSearch = () => {
	pagination.page = 1
	reqParams.skipCount = 0
	reqParams.name = form.value.name
}

const departmentListChange = () => {}
const activeIndex = ref(-1)
const handlCreate = () => {
	nodeData.value.push({
		id: generateUniqueRandomId(),
		node: '',
		departmentName: '',
	})
	activeIndex.value = nodeData.value.length - 1
}
const generateUniqueRandomId = (): number => {
	let newId: number
	const existingIds = nodeData.value.map((item) => item.id)
	do {
		newId = Math.floor(Math.random() * 1000)
	} while (existingIds.includes(newId))
	return newId
}
const nodeClick = (row: any) => {
	activeIndex.value = nodeData.value.indexOf(row)
}
const handlDelete = (row: any) => {
	ElMessageBox.confirm('请确认是否删除？删除后不可恢复！', '删除')
		.then(async (type) => {
			if (type === 'confirm') {
				nodeData.value = nodeData.value.filter((item: any) => item.id !== row.id)
				return ElMessage.success('删除成功')
			}
		})
		.catch((err) => {
			console.log(err)
		})
}
const checkDepartment = (row: any) => {
	console.log(row)
	return ElMessage.success('查验成功')
}

const showTableLabel = ref(false)
const isAllocationRecord = ref(false)
const isAllocationReport = ref(false)
const isMenuPermissions = ref(false)
const isAllocationExplanation = ref(false)
const isPermissionDistributionChart = ref(false)
const isOperationVideo = ref(false)
const isLifeCycle = ref(false)
const isPermissionVersion = ref(false)
const isOperatingManual = ref(false)
const isYJModel = ref(false)
const isYJProblem = ref(false)
const isReminderContent = ref(false)
const isQXCategory = ref(false)
const isQXAllocationBackup = ref(false)
const isChangeLog = ref(false)
const isQXOperationRecord = ref(false)
const isTemporaryAuthorization = ref(false)
const isReceiveSettings = ref(false)
const isVolumeLicense = ref(false)
const isPermissionRecord = ref(false)
const isPermissionCancel = ref(false)
const isPermissionUsageReport = ref(false)
const isPermissionTemplate = ref(false)
const isPermissionComparison = ref(false)
const isPermissionComparisonReport = ref(false)
const isPermissionOptimizationRule = ref(false)
const isPermissionChangeNotification = ref(false)
const isPermissionApplicationReport = ref(false)
const isPermissionCombined=ref(false)
const isPermissionMergeRecord=ref(false)

const history = () => {
	router.push('/taskTransfer')
}

const goToAuthorizationHistory = () => {
	router.push('/authorizationHistory')
}
</script>
<template>
	<div class="permission-distribution">
		<Block
			title="自定义权限分配"
			:enable-fixed-height="true"
			@heightChanged="onBlockHeightChanged"
		>
			<template #topRight> </template>
			<template #expand>
				<div class="search">
					<Form
						v-model="form"
						:props="formProps"
						:column-count="2"
						:label-width="74"
						:enable-reset="false"
						confirm-text="查询"
						button-vertical="flowing"
						@submit="onSearch"
					></Form>
					<div>
						<el-button size="small" type="primary" @click="isReceiveSettings = true">
							数据管理岗接收设置
						</el-button>
						<el-button size="small" type="primary" @click="showDataPermissions = true">
							数据权限
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionVersion = true">
							权限版本
						</el-button>
						<el-button size="small" type="primary" @click="isAllocationRecord = true">
							分配记录
						</el-button>
						<el-button size="small" type="primary" @click="isAllocationReport = true">
							分配报告
						</el-button>
						<el-button size="small" type="primary" @click="isOperatingManual = true">
							权限操作手册
						</el-button>
						<el-button size="small" type="primary" @click="isOperationVideo = true">
							权限操作视频
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionUsageReport=true">
							权限运用报告
						</el-button>
						<el-button size="small" type="primary" @click="showTableLabel = true">
							赋权标签
						</el-button>
						<el-button size="small" type="primary" @click="isMenuPermissions = true">
							功能菜单权限
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionRecord=true">
							权限修改记录
						</el-button>
						<el-button size="small" type="primary" @click="isAllocationExplanation = true">
							权限分配说明
						</el-button>
						<el-button size="small" type="primary" @click="isLifeCycle = true">
							权限使用周期
						</el-button>
						
						<el-button size="small" type="primary" @click="isReminderContent = true">
							权限提醒内容
						</el-button>
						<el-button size="small" type="primary" @click="showOperatePermissions = true">
							操作权限
						</el-button>
						<el-button size="small" type="primary" @click="isYJModel = true">
							权限预警模型
						</el-button>
					
					</div>
					<div style="margin-top: 10px">
						<el-button size="small" type="primary" @click="isYJProblem = true">
							预警问题
						</el-button>
						<el-button size="small" type="primary" @click="isChangeLog = true">
							权限变更日志
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionDistributionChart = true">
							权限分布图表
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionTemplate=true">
							权限分配模板
						</el-button>
						<el-button size="small" type="primary" @click="isQXCategory = true">
							权限类别
						</el-button>
						<el-button size="small" type="primary" @click="isQXAllocationBackup = true">
							权限分配备份
						</el-button>
						<el-button size="small" type="primary" @click="isQXOperationRecord = true">
							权限操作纪录
						</el-button>
						<el-button size="small" type="primary" @click="isTemporaryAuthorization = true">
							临时授权
						</el-button>
						<el-button size="small" type="primary" @click="isVolumeLicense = true">
							批量授权
						</el-button>
						<el-button size="small" type="primary" @click="history">
							转派历史
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionCancel=true">
							取消授权
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionComparison=true">
							权限比对
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionComparisonReport=true">
							权限比对报告
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionOptimizationRule=true">
							权限优化规则
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionChangeNotification=true">
							权限变更通知
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionApplicationReport=true">
							权限应用报告
						</el-button>
					
					</div>
					<div style="margin-top: 10px">
						<el-button size="small" type="primary" @click="isPermissionCombined=true">
							权限合并
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionMergeRecord=true">
							权限合并记录
						</el-button>
						<el-button size="small" type="primary" @click="goToAuthorizationHistory">
							授权历史
						</el-button>
					</div>
				</div>
			</template>
			<TableV2
				ref="tableRef"
				url="/api/filling/plan-task"
				:columns="columns"
				:req-params="reqParams"
				:enable-toolbar="false"
				:height="tableHeight"
				:auto-height="true"
				:enable-own-button="false"
				:buttons="[{type: 'primary', label: '权限分发', code: 'permissionDistribution'}]"
				@click-button="onTableButtonClick"
				@before-complete="onBeforeComplete"
				@completed="
					() => {
						pagination.total = tableRef?.getTotal()
						getPlanTaskProgress()
					}
				"
			>
				<template #departmentReportingStatus="scoped">
					<LoadingTransition v-if="loading"></LoadingTransition>
					<template v-else>
						{{ scoped.row.departmentReportingStatus || '-' }}
					</template>
				</template>
				<template #endDate="scope">
					<template v-if="scope.row.status === 9">已完结</template>
					<template v-else-if="scope.row.status === 8">已手动终止</template>
					<template v-else-if="scope.row.status === 10">时间已截止</template>
					<template v-else>
						<template v-if="scope.row.remindDays === false">
							<span text="gray">时间已截止</span>
						</template>
						<template v-else>
							<span
								v-if="scope.row.remindDays > 7"
								style="color: #20a162"
								class="df aic"
							>
								<el-icon style="color: #20a162">
									<WarningFilled />
								</el-icon>
								剩余{{ scope.row.remindDays }}天
							</span>
							<span
								v-if="scope.row.remindDays <= 7 && scope.row.remindDays > 3"
								class="df aic"
								style="color: #ffaa18"
							>
								<el-icon style="color: #ffaa18">
									<WarningFilled />
								</el-icon>
								剩余{{ scope.row.remindDays }}天
							</span>
							<span
								v-if="scope.row.remindDays <= 3"
								class="df aic"
								style="color: #20a162"
							>
								<el-icon style="color: #a61b29">
									<WarningFilled />
								</el-icon>
								剩余{{ scope.row.remindDays }}天
							</span>
						</template>
					</template>

					<!-- {{ scope.rowData.remindDays }} -->
				</template>
				<template #status="scoped">
					<el-tag :type="ReportsFlowStatusType(scoped.row.status)">
						{{
							ReportsFlowStatus.find(
								(f) => f.value === scoped.row.status && f.type === 'plan'
							)?.label || '-'
						}}
					</el-tag>
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			></Pagination>
		</Block>
		<DataPermissions v-model="showDataPermissions" title="数据权限" width="1100" />
		<OperatePermissions v-model="showOperatePermissions" title="操作权限" width="1100" />
		<YJModel v-model="isYJModel" title="权限预警模型管理" width="1100" />
		<YJProblem v-model="isYJProblem" title="预警问题" width="1100" />
		<QXCategory v-model="isQXCategory" title="权限类别" width="1100" />
		<QXAllocationBackup v-model="isQXAllocationBackup" title="权限分配备份管理" width="1100" />
		<QXOperationRecord v-model="isQXOperationRecord" title="权限操作纪录" width="1100" />
		<TemporaryAuthorization v-model="isTemporaryAuthorization" title="临时授权" width="1100" />
		<AnalysisReportLabel v-model="showTableLabel" title="赋权标签" width="1100" />
		<AllocationRecord v-model="isAllocationRecord" title="分配记录" width="1100" />
		<AllocationReport v-model="isAllocationReport" title="分配报告" width="1100" />
		<MenuPermissions v-model="isMenuPermissions" title="功能菜单权限" width="1100" />
		<AllocationExplanation
			v-model="isAllocationExplanation"
			title="权限分配说明"
			width="1100"
		/>
		<PermissionDistributionChart
			v-model="isPermissionDistributionChart"
			title="权限分布图表"
			width="1100"
		/>

		<OperationVideo v-model="isOperationVideo" title="权限操作视频" width="1100" />
		<LifeCycle v-model="isLifeCycle" title="权限使用周期" width="1100" />
		<PermissionVersion v-model="isPermissionVersion" title="权限版本" width="1100" />
		<OperatingManual v-model="isOperatingManual" title="操作手册" width="1100" />
		<ReminderContent v-model="isReminderContent" title="提醒内容" width="1100" />
		<ChangeLog v-model="isChangeLog" title="变更日志权限" width="1100" />
		<ReceiveSettings v-model="isReceiveSettings" title="数据管理岗" width="800" />
		<VolumeLicense v-model="isVolumeLicense" title="批量授权" width="600" />
		<PermissionRecord v-model="isPermissionRecord" title="权限修改记录" width="1100" />
		<PermissionCancel v-model="isPermissionCancel" title="取消授权" width="1100" />
		<PermissionUsageReport v-model="isPermissionUsageReport" title="权限运用报告" width="1100" />
		<PermissionTemplate v-model="isPermissionTemplate" title="权限分配模板" width="1200" />
		<PermissionComparison v-model="isPermissionComparison" title="权限比对" width="1200" />
		<PermissionComparisonReport v-model="isPermissionComparisonReport" title="权限比对报告" width="1200" />
		<PermissionOptimizationRule v-model="isPermissionOptimizationRule" title="权限优化规则" width="1200" />
		<PermissionChangeNotification v-model="isPermissionChangeNotification" title="权限变更通知" width="1200" />
		<PermissionApplicationReport v-model="isPermissionApplicationReport" title="权限应用报告" width="1200" />
		<PermissionCombined v-model="isPermissionCombined" title="权限合并" width="600" />
		<PermissionMergeRecord v-model="isPermissionMergeRecord" title="权限合并记录" width="1200" />
		<Dialog
			v-model="show"
			title="权限分发"
			:destroy-on-close="true"
			@click-confirm="
				() => {
					ElMessage.success('分配成功')
					show = false
				}
			"
		>
			<FormItem
				:items="[
					{
						label: '填报部门/人员',
						prop: 'treeSelect',
						type: 'treeSelect',
						labelWidth: 110,
					},
					{
						label: '授权有效期',
						prop: 'select',
						type: 'select',
						labelWidth: 110,
						options: optionSelect,
					},
					{
						label: '填报节点部门',
						prop: 'node',
						type: '',
						labelWidth: 110,
					},
				]"
			>
				<template #form-treeSelect>
					<departmentFavoriteComp
						placeholder="请选择"
						:type="'modal'"
						:data="checkDepartmentList"
						:defaultCheckedData="defaultCheckedData"
						:defaultCheckUserData="defaultCheckUserData"
						@change="departmentListChange"
					></departmentFavoriteComp>
				</template>
				<template #form-node>
					<el-table :data="nodeData" @row-click="nodeClick">
						<el-table-column prop="node" label="节点">
							<template #default="scope">
								<el-select
									v-if="activeIndex === scope.$index"
									placeholder="请选择节点"
									v-model="scope.row.node"
									size="small"
								>
									<el-option label="节点1" value="节点1" />
									<el-option label="节点2" value="节点2" />
									<el-option label="节点3" value="节点3" />
								</el-select>
								<span v-else>{{ scope.row.node }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="departmentName" label="部门名称">
							<template #default="scope">
								<el-input
									v-if="activeIndex === scope.$index"
									v-model="scope.row.departmentName"
									placeholder="请输入部门名称"
									size="small"
								/>
								<span v-else>{{ scope.row.departmentName }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="operation" label="删除">
							<template #default="scope">
								<el-button type="text" @click="activeIndex = scope.$index"
									>编辑</el-button
								>
								<el-button type="text" @click="handlDelete(scope.row)"
									>删除</el-button
								>
								<el-button type="text" @click="checkDepartment(scope.row)"
									>查验</el-button
								>
							</template>
						</el-table-column>
					</el-table>
					<div class="add-btn" @click="handlCreate">+新增一行</div>
				</template>
			</FormItem>
		</Dialog>
	</div>
</template>
<route>
    {
		meta: {
			title: '自定义权限分配',
		},
	}
</route>
<style scoped lang="scss">
.search {
	min-height: 100px;
	// line-height: 1.5;
}
.add-btn {
	width: 100%;
	height: 30px;
	text-align: center;
	line-height: 30px;
	border: 1px solid #ebeef5;
	border-top: none;
	color: #1764ce;
	cursor: pointer;
}
</style>
